$(document).on('click','.slide', function(){
    var obj=$(this);
    obj.toggleClass('is-expanded');
});

$(document).on('click','.toggle-menu', function(){
    $('.left-menu').toggleClass('show-box');
});

$(document).on('click', function(e) {
    if (!$(e.target).closest('.toggle-menu').length) {
        $('.left-menu').removeClass('show-box');
    }
});

$(document).on('click','.toggle-search', function(){
    $('.top-area .search-box').toggleClass('show-box');
});

// ========================================
// AUTHENTICATION PAGES FUNCTIONS
// ========================================

// Common notification system
function showNotification(message, type) {
    const notification = $(`
        <div class="alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(notification);

    setTimeout(function() {
        notification.alert('close');
    }, 5000);
}

// Password toggle functionality
function initPasswordToggle() {
    $(document).on('click', '.password-toggle', function() {
        const passwordField = $(this).siblings('.form-control');
        const eyeIcon = $(this).find('i');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            eyeIcon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            eyeIcon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
}

// Password strength checker
function initPasswordStrength() {
    $(document).on('input', '#password', function() {
        const password = $(this).val();
        const strengthFill = $('#strengthFill');
        const strengthText = $('#strengthText');

        if (!strengthFill.length) return;

        let strength = 0;
        let text = '';
        let className = '';

        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;

        switch (strength) {
            case 0:
            case 1:
                text = 'Weak password';
                className = 'strength-weak';
                break;
            case 2:
                text = 'Fair password';
                className = 'strength-fair';
                break;
            case 3:
            case 4:
                text = 'Good password';
                className = 'strength-good';
                break;
            case 5:
                text = 'Strong password';
                className = 'strength-strong';
                break;
        }

        strengthFill.removeClass('strength-weak strength-fair strength-good strength-strong');
        if (password.length > 0) {
            strengthFill.addClass(className);
            strengthText.text(text);
        } else {
            strengthText.text('Password strength');
        }
    });
}

// Form focus effects
function initFormEffects() {
    $(document).on('focus', '.auth-page .form-control', function() {
        $(this).parent().addClass('focused');
    }).on('blur', '.auth-page .form-control', function() {
        $(this).parent().removeClass('focused');
    });

    // Email validation feedback
    $(document).on('input', 'input[type="email"]', function() {
        const email = $(this).val().trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && emailRegex.test(email)) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else if (email) {
            $(this).removeClass('is-valid').addClass('is-invalid');
        } else {
            $(this).removeClass('is-valid is-invalid');
        }
    });
}

// Login form handler
function initLoginForm() {
    $(document).on('submit', '#loginForm', function(e) {
        e.preventDefault();

        const email = $('#email').val().trim();
        const password = $('#password').val();
        const submitBtn = $(this).find('button[type="submit"]');

        if (!email || !password) {
            showNotification('Please fill in all required fields.', 'error');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }

        // Show loading state
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Signing in...');

        // Simulate API call
        setTimeout(function() {
            console.log('Login attempt:', { email: email, password: password });
            showNotification('Login successful! Redirecting...', 'success');

            setTimeout(function() {
                window.location.href = 'index.html';
            }, 1500);
        }, 2000);
    });
}

// Register form handler
function initRegisterForm() {
    $(document).on('submit', '#registerForm', function(e) {
        e.preventDefault();

        const firstName = $('#firstName').val().trim();
        const lastName = $('#lastName').val().trim();
        const email = $('#email').val().trim();
        const phone = $('#phone').val().trim();
        const password = $('#password').val();
        const confirmPassword = $('#confirmPassword').val();
        const agreeTerms = $('#agreeTerms').is(':checked');
        const submitBtn = $(this).find('button[type="submit"]');

        // Validation
        if (!firstName || !lastName || !email || !password || !confirmPassword) {
            showNotification('Please fill in all required fields.', 'error');
            return;
        }

        if (password.length < 8) {
            showNotification('Password must be at least 8 characters long.', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showNotification('Passwords do not match.', 'error');
            return;
        }

        if (!agreeTerms) {
            showNotification('Please agree to the Terms of Service.', 'error');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }

        // Show loading state
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Creating account...');

        // Simulate API call
        setTimeout(function() {
            console.log('Registration attempt:', {
                firstName: firstName,
                lastName: lastName,
                email: email,
                phone: phone,
                password: password
            });

            showNotification('Account created successfully! Redirecting to login...', 'success');

            setTimeout(function() {
                window.location.href = 'login.html';
            }, 2000);
        }, 2000);
    });
}

// Forgot password form handler
function initForgotPasswordForm() {
    $(document).on('submit', '#forgotPasswordForm', function(e) {
        e.preventDefault();

        const email = $('#email').val().trim();
        const submitBtn = $('#submitBtn');

        // Validation
        if (!email) {
            showNotification('Please enter your email address.', 'error');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }

        // Show loading state
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Sending...');

        // Simulate API call
        setTimeout(function() {
            console.log('Password reset request for:', email);

            // Show success state
            showSuccessState(email);

        }, 2000);
    });

    // Resend email functionality
    $(document).on('click', '#resendBtn', function() {
        const email = $('#emailSent').text();
        const resendBtn = $(this);

        resendBtn.prop('disabled', true);
        resendBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Resending...');

        setTimeout(function() {
            console.log('Resending password reset for:', email);
            showNotification('Reset link sent again! Check your email.', 'success');

            resendBtn.prop('disabled', false);
            resendBtn.html('Resend email');
        }, 1500);
    });
}

// Show success state for forgot password
function showSuccessState(email) {
    $('#initialState').addClass('d-none');
    $('#successState').removeClass('d-none');
    $('#emailSent').text(email);

    showNotification('Reset link sent! Check your email.', 'success');
}

// Lockscreen functionality
function initLockscreen() {
    // Update time and date
    function updateDateTime() {
        const now = new Date();

        // Format time
        const timeOptions = {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        const timeString = now.toLocaleTimeString('en-US', timeOptions);
        $('#currentTime').text(timeString);

        // Format date
        const dateOptions = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        const dateString = now.toLocaleDateString('en-US', dateOptions);
        $('#currentDate').text(dateString);

        // Set lock time (simulate it was locked 5 minutes ago)
        const lockTime = new Date(now.getTime() - 5 * 60000);
        const lockTimeString = lockTime.toLocaleTimeString('en-US', timeOptions);
        $('#lockTime').text(lockTimeString);
    }

    // Update time immediately and then every second
    if ($('#currentTime').length) {
        updateDateTime();
        setInterval(updateDateTime, 1000);
    }

    // Unlock form handler
    $(document).on('submit', '#unlockForm', function(e) {
        e.preventDefault();

        const password = $('#password').val();
        const submitBtn = $(this).find('button[type="submit"]');

        if (!password) {
            showNotification('Please enter your password.', 'error');
            return;
        }

        // Show loading state
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Unlocking...');

        // Simulate password verification
        setTimeout(function() {
            console.log('Unlock attempt with password:', password);

            // For demo purposes, accept any password with length >= 3
            if (password.length >= 3) {
                showNotification('Screen unlocked successfully!', 'success');

                setTimeout(function() {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                showNotification('Invalid password. Please try again.', 'error');
                submitBtn.prop('disabled', false);
                submitBtn.html('<i class="fas fa-unlock me-2"></i>Unlock Screen');
                $('#password').val('').focus();
            }
        }, 1500);
    });

    // Auto-focus password field and add visual feedback
    $(document).on('input', '#password', function() {
        const password = $(this).val();
        if (password.length > 0) {
            $(this).addClass('is-valid');
        } else {
            $(this).removeClass('is-valid');
        }
    });

    // Keyboard shortcut for Enter key
    $(document).on('keypress', function(e) {
        if (e.which === 13 && $('#unlockForm').length && !$('#unlockForm button[type="submit"]').prop('disabled')) {
            $('#unlockForm').submit();
        }
    });
}

// Initialize all authentication functions
$(document).ready(function() {
    initPasswordToggle();
    initPasswordStrength();
    initFormEffects();
    initLoginForm();
    initRegisterForm();
    initForgotPasswordForm();
    initLockscreen();

    // Auto-focus first input on auth pages
    if ($('.auth-page').length) {
        setTimeout(function() {
            $('.auth-page .form-control:first').focus();
        }, 100);
    }
});
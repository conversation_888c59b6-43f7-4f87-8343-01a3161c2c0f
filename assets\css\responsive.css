/* Responsive Design */
@media(max-width: 768px){
    .left-menu{
        display: none;
    }
    .top-area {
        width: calc(100vw - 40px);
    }
    .m-show{
        display: block;
    }
    .m-hide{
        display: none;
    }
    .icon-button{
        width: 50px;
        height: 50px;
    }
    .btn{
        height: 50px;
    }
    .profile-menu div + div{
        display: none !important;
    }
    .top-area .col-md-4.show-box{
        order: 2;
        margin-top: 15px;
    }
    .top-area .col-md-8.text-end.d-flex.justify-content-end{
        order: 1;
    }
    .left-menu {
        margin-left: -5px;
    }
    .footer {
        width: calc(100vw - 40px);
        text-align: center;
        z-index: -1;
    }
    .footer div{
        text-align: center !important;
    }
    .error-section h1{
        font-size: 115px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .auth-card {
        padding: 32px 24px;
    }

    .auth-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .auth-actions .auth-link {
        justify-content: center;
    }

    .main-content {
        width: 100%;
        max-height: calc(100% - 248px);
    }

}